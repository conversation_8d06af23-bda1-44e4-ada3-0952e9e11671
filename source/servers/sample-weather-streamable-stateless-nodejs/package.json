{"name": "sample-weather-nodejs", "version": "1.0.0", "bin": {"weather": "./build/index.js"}, "type": "module", "files": ["build"], "main": "index.js", "scripts": {"build": "tsc && chmod 755 build/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-dynamodb": "^3.788.0", "@aws-sdk/client-ssm": "^3.787.0", "@aws-sdk/util-dynamodb": "^3.788.0", "@hono/node-server": "^1.14.1", "@modelcontextprotocol/sdk": "^1.9.0", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "express": "^5.1.0", "fetch-to-node": "^2.1.0", "hono": "^4.7.8", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^22.14.1", "typescript": "^5.8.3"}}