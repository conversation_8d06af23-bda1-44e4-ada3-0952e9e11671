[project]
name = "sample-weather-python"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "boto3>=1.37.33",
    "cryptography>=44.0.2",
    "httpx>=0.28.1",
    "mcp[cli]>=1.6.0",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.0",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
]

[tool.ruff]
line-length = 99
extend-include = ["*.ipynb"]
exclude = [
    ".venv",
    "**/__pycache__",
    "**/node_modules",
    "**/dist",
    "**/build",
    "**/env",
    "**/.ruff_cache",
    "**/.venv",
    "**/.ipynb_checkpoints"
]
force-exclude = true

[tool.ruff.lint]
exclude = ["__init__.py"]
select = ["C", "D", "E", "F", "I", "W"]
ignore = ["C901", "E501", "E741", "F402", "F823", "D100", "D106"]

[tool.ruff.lint.isort]
lines-after-imports = 2
no-sections = true

[tool.ruff.lint.per-file-ignores]
"**/*.ipynb" = ["F704"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.format]
quote-style = "single"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true