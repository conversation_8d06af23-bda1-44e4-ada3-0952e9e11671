{"name": "guidance-for-remote-mcp-servers-on-aws", "version": "0.1.0", "bin": {"guidance-for-remote-mcp-servers-on-aws": "bin/app.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "^2.1014.0", "esbuild": "^0.25.4", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "aws-cdk-lib": "^2.195.0", "cdk-nag": "^2.35.69", "constructs": "^10.4.2", "fetch-to-node": "^2.1.0", "hono": "^4.7.8", "jose": "^6.0.11", "node-fetch": "^3.3.2", "source-map-support": "^0.5.21", "zod": "^3.24.4"}}